import AsyncStorage from '@react-native-async-storage/async-storage';

const EXPENSES_KEY = '@expenses';
const MEMBERS_KEY = '@members';

// Expense storage functions
export const saveExpenses = async (expenses) => {
  try {
    const jsonValue = JSON.stringify(expenses);
    await AsyncStorage.setItem(EXPENSES_KEY, jsonValue);
  } catch (e) {
    console.error('Error saving expenses:', e);
  }
};

export const getExpenses = async () => {
  try {
    const jsonValue = await AsyncStorage.getItem(EXPENSES_KEY);
    return jsonValue != null ? JSON.parse(jsonValue) : [];
  } catch (e) {
    console.error('Error loading expenses:', e);
    return [];
  }
};

export const addExpense = async (expense) => {
  try {
    const expenses = await getExpenses();
    const newExpense = {
      id: Date.now().toString(),
      ...expense,
      date: new Date().toISOString(),
    };
    expenses.push(newExpense);
    await saveExpenses(expenses);
    return newExpense;
  } catch (e) {
    console.error('Error adding expense:', e);
    throw e;
  }
};

export const deleteExpense = async (expenseId) => {
  try {
    const expenses = await getExpenses();
    const filteredExpenses = expenses.filter(expense => expense.id !== expenseId);
    await saveExpenses(filteredExpenses);
  } catch (e) {
    console.error('Error deleting expense:', e);
    throw e;
  }
};

// Members storage functions
export const saveMembers = async (members) => {
  try {
    const jsonValue = JSON.stringify(members);
    await AsyncStorage.setItem(MEMBERS_KEY, jsonValue);
  } catch (e) {
    console.error('Error saving members:', e);
  }
};

export const getMembers = async () => {
  try {
    const jsonValue = await AsyncStorage.getItem(MEMBERS_KEY);
    return jsonValue != null ? JSON.parse(jsonValue) : [];
  } catch (e) {
    console.error('Error loading members:', e);
    return [];
  }
};

export const addMember = async (memberName) => {
  try {
    const members = await getMembers();
    if (!members.includes(memberName)) {
      members.push(memberName);
      await saveMembers(members);
    }
    return members;
  } catch (e) {
    console.error('Error adding member:', e);
    throw e;
  }
};

// Clear all data
export const clearAllData = async () => {
  try {
    await AsyncStorage.removeItem(EXPENSES_KEY);
    await AsyncStorage.removeItem(MEMBERS_KEY);
  } catch (e) {
    console.error('Error clearing data:', e);
  }
};
