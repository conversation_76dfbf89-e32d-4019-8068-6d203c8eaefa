// Calculate who owes whom based on expenses
export const calculateDebts = (expenses) => {
  const balances = {};
  const debts = [];

  // Initialize balances for all members
  expenses.forEach(expense => {
    expense.members.forEach(member => {
      if (!balances[member]) {
        balances[member] = 0;
      }
    });
    
    if (!balances[expense.paidBy]) {
      balances[expense.paidBy] = 0;
    }
  });

  // Calculate net balances
  expenses.forEach(expense => {
    const amountPerPerson = expense.amount / expense.members.length;
    
    // Person who paid gets credited
    balances[expense.paidBy] += expense.amount;
    
    // All members (including payer) get debited their share
    expense.members.forEach(member => {
      balances[member] -= amountPerPerson;
    });
  });

  // Convert balances to debts
  const creditors = [];
  const debtors = [];

  Object.entries(balances).forEach(([person, balance]) => {
    if (balance > 0.01) { // Small threshold to handle floating point precision
      creditors.push({ person, amount: balance });
    } else if (balance < -0.01) {
      debtors.push({ person, amount: Math.abs(balance) });
    }
  });

  // Match debtors with creditors
  creditors.forEach(creditor => {
    debtors.forEach(debtor => {
      if (creditor.amount > 0 && debtor.amount > 0) {
        const transferAmount = Math.min(creditor.amount, debtor.amount);
        
        if (transferAmount > 0.01) {
          debts.push({
            from: debtor.person,
            to: creditor.person,
            amount: Math.round(transferAmount * 100) / 100, // Round to 2 decimal places
          });
          
          creditor.amount -= transferAmount;
          debtor.amount -= transferAmount;
        }
      }
    });
  });

  return debts;
};

// Calculate total expenses
export const calculateTotalExpenses = (expenses) => {
  return expenses.reduce((total, expense) => total + expense.amount, 0);
};

// Calculate expenses by member
export const calculateExpensesByMember = (expenses) => {
  const memberExpenses = {};

  expenses.forEach(expense => {
    const amountPerPerson = expense.amount / expense.members.length;
    
    expense.members.forEach(member => {
      if (!memberExpenses[member]) {
        memberExpenses[member] = 0;
      }
      memberExpenses[member] += amountPerPerson;
    });
  });

  return memberExpenses;
};

// Calculate how much each person paid
export const calculateAmountsPaid = (expenses) => {
  const amountsPaid = {};

  expenses.forEach(expense => {
    if (!amountsPaid[expense.paidBy]) {
      amountsPaid[expense.paidBy] = 0;
    }
    amountsPaid[expense.paidBy] += expense.amount;
  });

  return amountsPaid;
};
